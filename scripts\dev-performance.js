#!/usr/bin/env node

import { spawn } from 'child_process'
import { performance } from 'perf_hooks'

console.log('🚀 启动性能监控的开发服务器...\n')

// 记录启动时间
const startTime = performance.now()
let cssGenerationTimes = []
let hmrTimes = []

// 启动开发服务器
const devProcess = spawn('npm', ['run', 'dev'], {
  stdio: 'pipe',
  shell: true
})

let isServerReady = false

devProcess.stdout.on('data', (data) => {
  const output = data.toString()
  console.log(output)
  
  // 检测服务器启动完成
  if (output.includes('Local:') && !isServerReady) {
    const endTime = performance.now()
    console.log(`\n⏱️  服务器启动时间: ${(endTime - startTime).toFixed(2)}ms\n`)
    isServerReady = true
  }
  
  // 监控 CSS 生成时间
  if (output.includes('tailwind') || output.includes('css')) {
    const timestamp = performance.now()
    cssGenerationTimes.push(timestamp)
    
    if (cssGenerationTimes.length > 1) {
      const lastTime = cssGenerationTimes[cssGenerationTimes.length - 2]
      const duration = timestamp - lastTime
      console.log(`🎨 CSS 处理时间: ${duration.toFixed(2)}ms`)
    }
  }
  
  // 监控 HMR 时间
  if (output.includes('hmr') || output.includes('update')) {
    const timestamp = performance.now()
    hmrTimes.push(timestamp)
    
    if (hmrTimes.length > 1) {
      const lastTime = hmrTimes[hmrTimes.length - 2]
      const duration = timestamp - lastTime
      console.log(`🔄 HMR 更新时间: ${duration.toFixed(2)}ms`)
    }
  }
})

devProcess.stderr.on('data', (data) => {
  const output = data.toString()
  console.error(output)
  
  // 检测 Tailwind 相关错误
  if (output.includes('tailwind') || output.includes('css')) {
    console.error('❌ Tailwind CSS 相关错误detected!')
  }
})

devProcess.on('close', (code) => {
  console.log(`\n📊 性能统计:`)
  console.log(`CSS 生成次数: ${cssGenerationTimes.length}`)
  console.log(`HMR 更新次数: ${hmrTimes.length}`)
  
  if (cssGenerationTimes.length > 1) {
    const avgCssTime = cssGenerationTimes.reduce((sum, time, index) => {
      if (index === 0) return sum
      return sum + (time - cssGenerationTimes[index - 1])
    }, 0) / (cssGenerationTimes.length - 1)
    console.log(`平均 CSS 处理时间: ${avgCssTime.toFixed(2)}ms`)
  }
  
  if (hmrTimes.length > 1) {
    const avgHmrTime = hmrTimes.reduce((sum, time, index) => {
      if (index === 0) return sum
      return sum + (time - hmrTimes[index - 1])
    }, 0) / (hmrTimes.length - 1)
    console.log(`平均 HMR 时间: ${avgHmrTime.toFixed(2)}ms`)
  }
  
  console.log(`\n进程退出，代码: ${code}`)
})

// 优雅退出
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭开发服务器...')
  devProcess.kill('SIGINT')
})
