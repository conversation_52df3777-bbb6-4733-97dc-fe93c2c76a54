import type { Config } from 'tailwindcss'

export default {
  // 启用调试模式
  debug: process.env.NODE_ENV === 'development',
  
  // 内容扫描路径 - 这是性能的关键
  content: [
    './app/**/*.{js,vue,ts}',
    './components/**/*.{js,vue,ts}',
    './layouts/**/*.vue',
    './pages/**/*.vue',
    './plugins/**/*.{js,ts}',
    './nuxt.config.{js,ts}',
    './app.vue',
    './error.vue'
  ],
  
  // 主题配置
  theme: {
    extend: {
      // 只保留必要的扩展
    }
  },
  
  // 插件
  plugins: [],
  
  // 性能优化选项
  experimental: {
    // 启用优化的内容扫描
    optimizeUniversalDefaults: true,
  },
  
  // 缓存配置
  cacheDir: './.tailwind-cache',
} satisfies Config
