#!/usr/bin/env node

import { readFileSync, existsSync } from 'fs'
import { glob } from 'glob'
import { gzipSync } from 'zlib'

console.log('📏 分析 CSS 文件大小...\n')

// 查找生成的 CSS 文件
const cssFiles = glob.sync('.nuxt/dist/client/**/*.css')
const outputCssFiles = glob.sync('.output/public/**/*.css')

const allCssFiles = [...cssFiles, ...outputCssFiles]

if (allCssFiles.length === 0) {
  console.log('⚠️  未找到生成的 CSS 文件，请先运行 npm run build')
  process.exit(1)
}

console.log('📁 找到的 CSS 文件:')
allCssFiles.forEach(file => console.log(`  - ${file}`))
console.log()

let totalSize = 0
let totalGzipSize = 0
const fileAnalysis = []

allCssFiles.forEach(file => {
  if (existsSync(file)) {
    const content = readFileSync(file, 'utf-8')
    const size = Buffer.byteLength(content, 'utf-8')
    const gzipSize = gzipSync(content).length
    
    totalSize += size
    totalGzipSize += gzipSize
    
    fileAnalysis.push({
      file,
      size,
      gzipSize,
      sizeKB: (size / 1024).toFixed(2),
      gzipSizeKB: (gzipSize / 1024).toFixed(2)
    })
    
    console.log(`📄 ${file}:`)
    console.log(`   原始大小: ${(size / 1024).toFixed(2)} KB`)
    console.log(`   Gzip 大小: ${(gzipSize / 1024).toFixed(2)} KB`)
    console.log(`   压缩率: ${((1 - gzipSize / size) * 100).toFixed(1)}%`)
    console.log()
  }
})

console.log('📊 总计:')
console.log(`   总原始大小: ${(totalSize / 1024).toFixed(2)} KB`)
console.log(`   总 Gzip 大小: ${(totalGzipSize / 1024).toFixed(2)} KB`)
console.log(`   总压缩率: ${((1 - totalGzipSize / totalSize) * 100).toFixed(1)}%`)

// 分析 CSS 内容
console.log('\n🔍 CSS 内容分析:')
allCssFiles.forEach(file => {
  if (existsSync(file)) {
    const content = readFileSync(file, 'utf-8')
    
    // 统计规则数量
    const rules = content.match(/[^{}]+\{[^{}]*\}/g) || []
    console.log(`📄 ${file}:`)
    console.log(`   CSS 规则数量: ${rules.length}`)
    
    // 统计选择器类型
    const classSelectors = content.match(/\.[a-zA-Z0-9_-]+/g) || []
    const idSelectors = content.match(/#[a-zA-Z0-9_-]+/g) || []
    const elementSelectors = content.match(/^[a-zA-Z][a-zA-Z0-9]*\s*\{/gm) || []
    
    console.log(`   类选择器: ${classSelectors.length}`)
    console.log(`   ID选择器: ${idSelectors.length}`)
    console.log(`   元素选择器: ${elementSelectors.length}`)
    
    // 检查是否包含未使用的样式
    const unusedPatterns = [
      /\.unused-/g,
      /\.test-/g,
      /\.debug-/g
    ]
    
    let unusedCount = 0
    unusedPatterns.forEach(pattern => {
      const matches = content.match(pattern) || []
      unusedCount += matches.length
    })
    
    if (unusedCount > 0) {
      console.log(`   ⚠️  可能未使用的样式: ${unusedCount}`)
    }
    
    console.log()
  }
})

// 提供优化建议
console.log('💡 优化建议:')

if (totalSize > 300 * 1024) {
  console.log('❌ CSS 文件过大 (>300KB)，建议优化')
  console.log('   - 检查是否有未使用的样式')
  console.log('   - 考虑代码分割')
  console.log('   - 使用 PurgeCSS 移除未使用的样式')
}

if (totalGzipSize > 100 * 1024) {
  console.log('⚠️  Gzip 后仍然较大 (>100KB)')
  console.log('   - 检查重复的样式规则')
  console.log('   - 优化 CSS 结构')
}

const compressionRatio = (1 - totalGzipSize / totalSize) * 100
if (compressionRatio < 70) {
  console.log('⚠️  压缩率较低 (<70%)')
  console.log('   - CSS 可能包含大量重复内容')
  console.log('   - 考虑使用 CSS 优化工具')
}

console.log('\n✅ 分析完成！')
